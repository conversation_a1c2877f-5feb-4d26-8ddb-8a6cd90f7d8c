"""Consolidated mock data and utilities for testing."""
import base64
from typing import Any, Dict, List, Optional, Union

# Common test data constants
TEST_API_KEY = "test-api-key-123"
TEST_MODEL_ID = "x-ai/grok-4"

# API URLs
CHAT_COMPLETIONS_URL = "https://api.x.ai/v1/chat/completions"
MESSAGES_URL = "https://api.x.ai/v1/messages"

# Sample image data
SAMPLE_JPEG_BASE64 = "/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAEBAQ=="
SAMPLE_PNG_BASE64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
SAMPLE_IMAGE_URL = "https://example.com/image.jpg"
SAMPLE_DATA_URL = f"data:image/png;base64,{SAMPLE_PNG_BASE64}"
INVALID_BASE64 = "not-valid-base64!"

# Common error responses
ERROR_RESPONSES = {
    "invalid_request": {
        "error": {
            "message": "Invalid request",
            "type": "invalid_request_error",
            "code": "invalid_api_key",
            "param": None
        }
    },
    "rate_limit": {
        "error": {
            "type": "rate_limit_error",
            "message": "Rate limit exceeded. Please wait and try again.",
            "param": None,
            "code": None
        }
    },
    "quota_exceeded": {
        "error": {
            "type": "quota_exceeded_error",
            "message": "You have exceeded your usage quota.",
            "param": None,
            "code": None
        }
    }
}

# Sample tool definitions
SAMPLE_TOOLS = {
    "get_weather": {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get the current weather for a location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city or location to get weather for"
                    }
                },
                "required": ["location"]
            }
        }
    },
    "calculate": {
        "type": "function",
        "function": {
            "name": "calculate",
            "description": "Perform a mathematical calculation",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "The mathematical expression to evaluate"
                    }
                },
                "required": ["expression"]
            }
        }
    }
}

# Sample tool calls
SAMPLE_TOOL_CALLS = {
    "get_weather": {
        "id": "call_1",
        "type": "function",
        "function": {
            "name": "get_weather",
            "arguments": '{"location": "San Francisco"}'
        }
    },
    "calculate": {
        "id": "call_2",
        "type": "function",
        "function": {
            "name": "calculate",
            "arguments": '{"expression": "2 + 2"}'
        }
    }
}

# Sample streaming chunks
def create_streaming_chunks(
    content: str,
    include_tool_calls: bool = False,
    chunk_id: str = "chatcmpl-123"
) -> List[bytes]:
    """Create SSE chunks for testing streaming responses."""
    chunks = []
    
    # Initial chunk with role
    chunks.append(f'data: {{"id":"{chunk_id}","choices":[{{"delta":{{"role":"assistant"}}}}]}}\n\n'.encode())
    
    # Content chunks
    words = content.split()
    for i, word in enumerate(words):
        if i > 0:
            chunks.append(f'data: {{"id":"{chunk_id}","choices":[{{"delta":{{"content":" "}}}}]}}\n\n'.encode())
        chunks.append(f'data: {{"id":"{chunk_id}","choices":[{{"delta":{{"content":"{word}"}}}}]}}\n\n'.encode())
    
    # Tool calls if requested
    if include_tool_calls:
        chunks.append(b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"id":"call_1","type":"function","function":{"name":"get_weather"}}]}}]}\n\n')
        chunks.append(b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\\"location\\": \\"NYC\\"}"}}]}}]}\n\n')
    
    # Done chunk
    chunks.append(b"data: [DONE]\n\n")
    
    return chunks


def create_anthropic_streaming_chunks(
    content: str,
    message_id: str = "msg_123"
) -> List[bytes]:
    """Create Anthropic-style SSE chunks for testing."""
    chunks = []
    
    # Message start
    chunks.append(f'event: message_start\ndata: {{"message": {{"id": "{message_id}", "model": "x-ai/grok-4"}}}}\n\n'.encode())
    
    # Content block start
    chunks.append(b'event: content_block_start\ndata: {"index": 0, "content_block": {"type": "text", "text": ""}}\n\n')
    
    # Content deltas
    words = content.split()
    for i, word in enumerate(words):
        if i > 0:
            chunks.append(b'event: content_block_delta\ndata: {"index": 0, "delta": {"type": "text_delta", "text": " "}}\n\n')
        chunks.append(f'event: content_block_delta\ndata: {{"index": 0, "delta": {{"type": "text_delta", "text": "{word}"}}}}\n\n'.encode())
    
    # Content block stop
    chunks.append(b'event: content_block_stop\ndata: {"index": 0}\n\n')
    
    # Message stop
    chunks.append(b'event: message_stop\ndata: {}\n\n')
    
    return chunks


def create_chat_completion_response(
    content: str,
    model: str = TEST_MODEL_ID,
    finish_reason: str = "stop",
    tool_calls: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Create a mock chat completion response."""
    response: Dict[str, Any] = {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": model,
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": content
            },
            "finish_reason": finish_reason,
        }],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }
    
    if tool_calls:
        response["choices"][0]["message"]["tool_calls"] = tool_calls
        
    return response


def create_messages_response(
    content: str,
    model: str = TEST_MODEL_ID,
    stop_reason: str = "end_turn",
    tool_calls: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Create a mock Anthropic-style messages response."""
    response_content: List[Dict[str, Any]] = [{"type": "text", "text": content}]
    
    # Add tool calls in Anthropic format
    if tool_calls:
        for tool_call in tool_calls:
            response_content.append({
                "type": "tool_use",
                "id": tool_call.get("id", "toolu_123"),
                "name": tool_call.get("function", {}).get("name", ""),
                "input": tool_call.get("function", {}).get("arguments", {})
            })
    
    return {
        "id": "msg_123",
        "type": "message",
        "model": model,
        "role": "assistant",
        "content": response_content,
        "stop_reason": stop_reason,
        "stop_sequence": None,
        "usage": {
            "input_tokens": 10,
            "output_tokens": 20
        }
    }


def create_multimodal_message(
    text: str,
    image_urls: Optional[List[str]] = None,
    base64_images: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a multimodal message with text and images."""
    content: List[Dict[str, Any]] = [{"type": "text", "text": text}]
    
    if image_urls:
        for url in image_urls:
            content.append({
                "type": "image_url",
                "image_url": {"url": url}
            })
    
    if base64_images:
        for b64_data in base64_images:
            # Detect MIME type from base64 data
            mime_type = "image/jpeg" if b64_data.startswith("/9j/") else "image/png"
            content.append({
                "type": "image_url",
                "image_url": {"url": f"data:{mime_type};base64,{b64_data}"}
            })
    
    return {"role": "user", "content": content}


def create_test_request(
    prompt: str,
    model: str = TEST_MODEL_ID,
    stream: bool = False,
    temperature: Optional[float] = None,
    max_completion_tokens: Optional[int] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
    response_format: Optional[Dict[str, str]] = None,
    reasoning_effort: Optional[str] = None,
    system: Optional[str] = None,
    conversation_history: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Create a complete test request body."""
    messages = []
    
    # Add system message if provided
    if system:
        messages.append({"role": "system", "content": system})
    
    # Add conversation history if provided
    if conversation_history:
        messages.extend(conversation_history)
    
    # Add user message
    messages.append({"role": "user", "content": prompt})
    
    # Build request body
    body: Dict[str, Any] = {
        "model": model,
        "messages": messages,
        "stream": stream,
        "temperature": temperature or 0.0
    }
    
    # Add optional parameters
    if max_completion_tokens is not None:
        body["max_completion_tokens"] = max_completion_tokens
    
    if tools:
        body["tools"] = tools
        if tool_choice is not None:
            body["tool_choice"] = tool_choice
    
    if response_format:
        body["response_format"] = response_format
    
    if reasoning_effort:
        body["reasoning_effort"] = reasoning_effort
    
    return body


# Retry and timeout constants
DEFAULT_RETRY_DELAY = 60  # seconds
MAX_RETRY_DELAY = 120  # seconds
DEFAULT_TIMEOUT = 30  # seconds

# Headers for different error scenarios
RATE_LIMIT_HEADERS = {"Retry-After": str(DEFAULT_RETRY_DELAY)}
RATE_LIMIT_HEADERS_HIGH = {"Retry-After": str(MAX_RETRY_DELAY)}