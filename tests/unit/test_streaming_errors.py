"""Test streaming error scenarios and edge cases."""
import json
from unittest.mock import Mock, patch
from typing import Iterator, List, Any, Dict

import pytest
import httpx

from llm_grok.processors.streaming import StreamProcessor
from llm_grok.exceptions import GrokError, ValidationError


class MockResponse:
    """Mock response object that properly handles dynamic attributes."""
    
    def __init__(self) -> None:
        self._tool_calls_accumulator: List[Dict[str, Any]] = []
        self.tool_calls: List[Dict[str, Any]] = []
        
    def __setattr__(self, name: str, value: Any) -> None:
        self.__dict__[name] = value
        
    def __getattr__(self, name: str) -> Any:
        if name in self.__dict__:
            return self.__dict__[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")


class TestStreamingErrorScenarios:
    """Test error handling in streaming responses."""
    
    # Declare instance variable that will be initialized in setup_method
    processor: StreamProcessor  # pyright: ignore[reportUninitializedInstanceVariable]
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.processor = StreamProcessor(model_id="x-ai/grok-4")
    
    def test_malformed_json_chunks(self) -> None:
        """Test handling of malformed JSON in stream."""
        malformed_chunks = [
            b'data: {"choices": [{"delta": {"content": "test"',  # Incomplete JSON
            b'data: {"choices": [{"delta": {"content": "test"}}}',  # Missing closing ]
            b'data: {invalid json}',  # Invalid JSON
            b'data: {"choices": [{"delta": null}]}',  # Null delta
            b'data: {"choices": []}',  # Empty choices
            b'data: {}',  # Missing choices
            b'data: [DONE]'
        ]
        
        accumulated_content = ""
        errors_caught = 0
        events = []
        
        # Create a byte stream iterator
        def stream_iter() -> Iterator[bytes]:
            for chunk in malformed_chunks:
                yield chunk
        
        # Process the stream
        try:
            for event in self.processor.process(stream_iter()):
                events.append(event)
                if event["type"] == "content":
                    accumulated_content += event["data"]["text"]
        except Exception:
            errors_caught += 1
        
        # Should handle some malformed data gracefully
        assert len(events) >= 0  # May get some valid events before errors
    
    def test_buffer_overflow_protection(self) -> None:
        """Test protection against buffer overflow in streaming."""
        from llm_grok.client import GrokClient
        max_buffer_size = GrokClient.MAX_BUFFER_SIZE
        
        # Create an iterator that simulates a stream with large data
        def large_stream() -> Iterator[bytes]:
            # Create a malformed chunk that will never be consumed from buffer
            # This simulates a case where the SSE parser can't find a complete event
            # and the buffer keeps growing
            incomplete_data = "data: " + "x" * (max_buffer_size + 1000)
            # No newlines means it won't be parsed as complete SSE
            yield incomplete_data.encode()
        
        # Process should yield an error event for oversized buffer
        events = list(self.processor.process(large_stream()))
        
        # Check for error event
        error_events = [e for e in events if e["type"] == "error"]
        assert len(error_events) > 0
        assert "buffer exceeded" in error_events[0]["error"].lower()
    
    def test_connection_interruption_recovery(self) -> None:
        """Test handling of connection interruptions during streaming."""
        chunks = [
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',
            b'data: {"choices": [{"delta": {"content": " world"}}]}', 
            # Simulated interruption - incomplete data
            b'data: {"choices": [{"delta": {"con',
        ]
        
        content_parts = []
        
        # Create byte stream
        def stream_iter() -> Iterator[bytes]:
            for chunk in chunks:
                yield chunk + b'\n\n'
        
        # Process stream
        events = list(self.processor.process(stream_iter()))
        
        # Extract content
        for event in events:
            if event["type"] == "content":
                content_parts.append(event["data"]["text"])
        
        # Should have processed the complete chunks
        assert content_parts == ["Hello", " world"]
    
    def test_out_of_order_tool_calls(self) -> None:
        """Test handling of out-of-order tool call chunks."""
        # Create a mock response object to accumulate tool calls
        response = MockResponse()
        
        # Tool calls arriving out of sequence
        deltas = [
            {
                "tool_calls": [{
                    "index": 1,  # Second tool call arrives first
                    "id": "call_2",
                    "function": {"name": "tool2", "arguments": '{"arg": '}
                }]
            },
            {
                "tool_calls": [{
                    "index": 0,  # First tool call arrives second
                    "id": "call_1", 
                    "function": {"name": "tool1", "arguments": '{"param": "value"}'}
                }]
            },
            {
                "tool_calls": [{
                    "index": 1,  # Continue second tool call
                    "function": {"arguments": '"data"}'}
                }]
            }
        ]
        
        # Process deltas using the internal method
        for delta in deltas:
            self.processor._process_stream_delta(delta, response)
        
        # Finalize tool calls
        self.processor._finalize_tool_calls(response)
        
        # Get finalized tool calls
        finalized = response.tool_calls
        
        # Should have both tool calls in correct order
        assert len(finalized) == 2
        assert finalized[0]["id"] == "call_1"
        assert finalized[0]["function"]["name"] == "tool1"
        assert finalized[1]["id"] == "call_2"
        assert finalized[1]["function"]["name"] == "tool2"
    
    def test_mixed_content_and_tool_calls(self) -> None:
        """Test complex interleaving of content and tool calls."""
        chunks = [
            b'data: {"choices": [{"delta": {"content": "Let me help you with that. "}}]}',
            b'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "id": "call_1", "function": {"name": "search", "arguments": "{\\"query\\": \\""}}]}}]}',
            b'data: {"choices": [{"delta": {"content": "I\'ll search for "}}]}',
            b'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "function": {"arguments": "test query\\"}"}}]}}]}',
            b'data: {"choices": [{"delta": {"content": "the information."}}]}',
            b'data: {"choices": [{"delta": {"tool_calls": [{"index": 1, "id": "call_2", "function": {"name": "calculate", "arguments": "{\\"num\\": 42}"}}]}}]}',
        ]
        
        contents = []
        tool_calls = []
        
        # Create byte stream
        def stream_iter() -> Iterator[bytes]:
            for chunk in chunks:
                yield chunk + b'\n\n'
        
        # Process stream and collect events
        for event in self.processor.process(stream_iter()):
            if event["type"] == "content":
                contents.append(event["data"]["text"])
            elif event["type"] == "tool_calls":
                tool_calls.extend(event["data"]["calls"])
        
        # Should have collected all content
        full_content = "".join(contents)
        assert "Let me help you with that." in full_content
        assert "I'll search for" in full_content
        assert "the information." in full_content
        
        # Should have both tool calls
        assert len(tool_calls) == 2
        assert tool_calls[0]["function"]["name"] == "search"
        assert tool_calls[1]["function"]["name"] == "calculate"
    
    def test_special_characters_in_tool_arguments(self) -> None:
        """Test handling of special characters in tool call arguments."""
        # Create a mock response
        response = MockResponse()
        
        special_cases = [
            '{"text": "Hello\\nWorld"}',  # Newlines
            '{"path": "C:\\\\Users\\\\<USER>\\"quoted\\" text"}',  # Escaped quotes
            '{"json": "{\\"nested\\": true}"}',  # Nested JSON
        ]
        
        for i, args in enumerate(special_cases):
            delta = {
                "tool_calls": [{
                    "index": i,
                    "id": f"call_{i}",
                    "function": {"name": f"tool_{i}", "arguments": args}
                }]
            }
            
            # Process delta
            self.processor._process_stream_delta(delta, response)
        
        # All tool calls should be processed correctly
        self.processor._finalize_tool_calls(response)
        tool_calls = response.tool_calls
        assert len(tool_calls) == len(special_cases)
        
        # Verify arguments are properly parsed
        for i, tc in enumerate(tool_calls):
            args = json.loads(tc["function"]["arguments"])
            assert isinstance(args, dict)
    
    def test_extremely_long_tool_arguments(self) -> None:
        """Test handling of very long tool call arguments split across chunks."""
        # Create a mock response
        response = MockResponse()
        
        # Create a large argument that will be split
        large_data = {"items": ["item_" + str(i) for i in range(1000)]}
        large_json = json.dumps(large_data)
        
        # Split into chunks
        chunk_size = 100
        deltas = []
        
        # First delta with tool call start
        deltas.append({
            "tool_calls": [{
                "index": 0,
                "id": "call_large",
                "function": {"name": "process_data", "arguments": large_json[:chunk_size]}
            }]
        })
        
        # Middle deltas with continued arguments
        for i in range(chunk_size, len(large_json), chunk_size):
            deltas.append({
                "tool_calls": [{
                    "index": 0,
                    "function": {"arguments": large_json[i:i+chunk_size]}
                }]
            })
        
        # Process all deltas
        for delta in deltas:
            self.processor._process_stream_delta(delta, response)
        
        # Verify the complete argument was accumulated
        self.processor._finalize_tool_calls(response)
        tool_calls = response.tool_calls
        assert len(tool_calls) == 1
        
        parsed_args = json.loads(tool_calls[0]["function"]["arguments"])
        assert len(parsed_args["items"]) == 1000
        assert parsed_args["items"][0] == "item_0"
        assert parsed_args["items"][-1] == "item_999"
    
    def test_empty_and_whitespace_chunks(self) -> None:
        """Test handling of empty and whitespace-only chunks."""
        chunks = [
            b'data: {"choices": [{"delta": {"content": ""}}]}',  # Empty content
            b'data: {"choices": [{"delta": {"content": "   "}}]}',  # Whitespace
            b'data: {"choices": [{"delta": {"content": "\\n\\n"}}]}',  # Newlines
            b'data: {"choices": [{"delta": {"content": "\\t"}}]}',  # Tab
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',  # Real content
            b'data: {"choices": [{"delta": {}}]}',  # Empty delta
            b'data: {"choices": [{"delta": {"content": null}}]}',  # Null content
        ]
        
        contents = []
        
        # Create byte stream
        def stream_iter() -> Iterator[bytes]:
            for chunk in chunks:
                yield chunk + b'\n\n'
        
        # Process stream
        for event in self.processor.process(stream_iter()):
            if event["type"] == "content":
                contents.append(event["data"]["text"])
        
        # Should preserve most content but handle null/empty gracefully
        # Note: The processor may filter out empty strings and convert escape sequences
        assert "   " in contents  # Whitespace
        assert "\n\n" in contents  # Newlines (converted from \\n\\n)
        assert "\t" in contents  # Tab (converted from \\t)
        assert "Hello" in contents  # Real content
    
    def test_concurrent_stream_processing(self) -> None:
        """Test thread safety of stream processing."""
        import threading
        import time
        
        # Shared processor (not recommended in practice)
        processor = StreamProcessor(model_id="x-ai/grok-4")
        results = []
        errors = []
        
        def process_stream(stream_id: int, chunks: List[bytes]) -> None:
            try:
                # Create byte stream
                def stream_iter() -> Iterator[bytes]:
                    for chunk in chunks:
                        yield chunk + b'\n\n'
                        time.sleep(0.001)  # Simulate network delay
                
                # Process stream
                for event in processor.process(stream_iter()):
                    if event["type"] == "content":
                        results.append((stream_id, event["data"]["text"]))
            except Exception as e:
                errors.append((stream_id, str(e)))
        
        # Create concurrent streams
        stream1_chunks = [
            b'data: {"choices": [{"delta": {"content": "Stream 1: "}}]}',
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',
        ]
        
        stream2_chunks = [
            b'data: {"choices": [{"delta": {"content": "Stream 2: "}}]}',
            b'data: {"choices": [{"delta": {"content": "World"}}]}',
        ]
        
        # Start threads
        thread1 = threading.Thread(target=process_stream, args=(1, stream1_chunks))
        thread2 = threading.Thread(target=process_stream, args=(2, stream2_chunks))
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        # Should have results from both streams (order may vary)
        assert len(results) >= 2  # At least some results
        stream_ids = set(r[0] for r in results)
        assert 1 in stream_ids or 2 in stream_ids  # At least one stream processed
    
    def test_error_recovery_in_tool_calls(self) -> None:
        """Test error recovery when tool calls are malformed."""
        # Create a mock response
        response = MockResponse()
        
        deltas = [
            # Valid tool call start
            {
                "tool_calls": [{
                    "index": 0,
                    "id": "call_1",
                    "function": {"name": "tool1", "arguments": "{"}
                }]
            },
            # Malformed arguments
            {
                "tool_calls": [{
                    "index": 0,
                    "function": {"arguments": "invalid json"}
                }]
            },
            # Try to continue with new tool call
            {
                "tool_calls": [{
                    "index": 1,
                    "id": "call_2",
                    "function": {"name": "tool2", "arguments": '{"valid": true}'}
                }]
            },
        ]
        
        # Process deltas
        for delta in deltas:
            try:
                self.processor._process_stream_delta(delta, response)
            except Exception:
                pass  # Continue processing
        
        # Should be able to finalize what's valid
        self.processor._finalize_tool_calls(response)
        tool_calls = response.tool_calls
        
        # Second tool call should be valid
        valid_calls = [tc for tc in tool_calls if tc.get("id") == "call_2"]
        assert len(valid_calls) == 1
        assert json.loads(valid_calls[0]["function"]["arguments"])["valid"] is True