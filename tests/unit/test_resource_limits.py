"""Tests for resource limit security features."""
import json
from typing import Iterator
from unittest.mock import Mock, patch
import pytest
import httpx

from llm_grok.client import GrokClient
from llm_grok.exceptions import RateLimitError, QuotaExceededError
from llm_grok.processors.streaming import StreamProcessor
from llm_grok.types import RequestBody
from tests.utils.mocks import (
    TEST_API_KEY,
    DEFAULT_RETRY_DELAY,
    MAX_RETRY_DELAY,
)


class TestResourceLimits:
    """Test resource limit enforcement."""

    # Type annotation with default - setup_method will override this
    client: GrokClient = GrokClient(api_key="default")

    def setup_method(self) -> None:
        """Set up test client."""
        self.client = GrokClient(api_key=TEST_API_KEY)
    
    def test_max_wait_time_cap(self) -> None:
        """Test that wait time is capped at MAX_WAIT_TIME."""
        # Create a mock response with high retry-after
        mock_response = Mock(spec=httpx.Response)
        mock_response.status_code = 429
        mock_response.headers = {"Retry-After": str(MAX_RETRY_DELAY)}
        
        # Mock the error parsing to avoid quota exceeded
        self.client._parse_error_response = Mock(return_value=("Rate limit hit", None))
        
        # Mock console and Progress to avoid output
        with patch('llm_grok.client.console'), \
             patch('llm_grok.client.Progress'), \
             patch('llm_grok.client.time.sleep') as mock_sleep:
            
            # Should cap at MAX_WAIT_TIME (60 seconds)
            result = self.client._handle_rate_limit(mock_response, attempt=0)
            assert result is True
            
            # Verify sleep was called with capped time
            mock_sleep.assert_called_once()
            sleep_time = mock_sleep.call_args[0][0]
            assert sleep_time == self.client.MAX_WAIT_TIME
    
    def test_exponential_backoff_cap(self) -> None:
        """Test that exponential backoff is also capped."""
        mock_response = Mock(spec=httpx.Response)
        mock_response.status_code = 429
        mock_response.headers = {}  # No retry-after header
        
        self.client._parse_error_response = Mock(return_value=("Rate limit", None))
        
        with patch('llm_grok.client.console'), \
             patch('llm_grok.client.Progress'), \
             patch('llm_grok.client.time.sleep') as mock_sleep:
            
            # Test high attempt number - should still cap
            # 2^10 = 1024 seconds, but should cap at 60
            result = self.client._handle_rate_limit(mock_response, attempt=10)
            assert result is True
            
            # Verify capped
            sleep_time = mock_sleep.call_args[0][0]
            assert sleep_time == self.client.MAX_WAIT_TIME
    
    def test_request_size_validation_pass(self) -> None:
        """Test that normal-sized requests pass validation."""
        # Create a request under the limit
        small_data: RequestBody = {"message": "test" * 100}  # Small request
        
        # Should not raise
        self.client._validate_request_size(small_data)
    
    def test_request_size_validation_fail(self) -> None:
        """Test that oversized requests are rejected."""
        # Create a request over 10MB limit
        large_data: RequestBody = {"message": "x" * (11 * 1024 * 1024)}  # 11MB of 'x'
        
        with pytest.raises(ValueError) as exc_info:
            self.client._validate_request_size(large_data)
        
        assert "exceeds maximum allowed size" in str(exc_info.value)
        assert str(self.client.MAX_REQUEST_SIZE) in str(exc_info.value)
    
    def test_request_size_edge_case(self) -> None:
        """Test request exactly at the size limit."""
        # Create request exactly at 10MB
        # Account for JSON overhead
        json_overhead = len('{"message":""}')
        content_size = self.client.MAX_REQUEST_SIZE - json_overhead
        exact_data: RequestBody = {"message": "x" * content_size}
        
        # Should not raise
        self.client._validate_request_size(exact_data)
        
        # Add one more byte - should fail
        exact_data["message"] += "x"  # type: ignore[operator]
        with pytest.raises(ValueError):
            self.client._validate_request_size(exact_data)
    
    def test_streaming_buffer_limit(self) -> None:
        """Test that streaming buffer size is limited."""
        processor = StreamProcessor("test-model")
        
        # Create a mock HTTP response that yields large chunks
        class MockResponse:
            def iter_raw(self) -> Iterator[bytes]:
                # Yield chunks that will exceed buffer
                chunk_size = 10 * 1024 * 1024  # 10MB chunks
                for _ in range(12):  # 120MB total, exceeds 100MB limit
                    yield b"x" * chunk_size
        
        mock_response = MockResponse()
        mock_llm_response = Mock()
        
        # Should raise when buffer exceeds limit
        with pytest.raises(ValueError) as exc_info:
            # Consume the generator to trigger the error
            list(processor.process_stream(mock_response, mock_llm_response, use_messages=False))
        
        assert "Streaming buffer exceeded maximum size" in str(exc_info.value)
        assert str(GrokClient.MAX_BUFFER_SIZE) in str(exc_info.value)
    
    def test_streaming_buffer_accumulation(self) -> None:
        """Test that buffer size is checked during accumulation."""
        processor = StreamProcessor("test-model")
        
        # Create response that sends data in small chunks but accumulates beyond limit
        class MockResponse:
            def iter_raw(self) -> Iterator[bytes]:
                # Send 1MB chunks, 110 times = 110MB total
                chunk_size = 1024 * 1024  # 1MB
                for _ in range(110):
                    yield b"x" * chunk_size
        
        mock_response = MockResponse()
        mock_llm_response = Mock()
        
        # Mock the formatters to return None (no valid SSE data)
        with patch.object(processor._openai_formatter, 'parse_openai_sse', return_value=(None, "")):
            with pytest.raises(ValueError) as exc_info:
                list(processor.process_stream(mock_response, mock_llm_response, use_messages=False))
            
            assert "Streaming buffer exceeded" in str(exc_info.value)
    
    def test_make_request_validates_size(self) -> None:
        """Test that make_request validates request size."""
        # Create oversized request
        large_data: RequestBody = {"data": "x" * (11 * 1024 * 1024)}
        
        with pytest.raises(ValueError) as exc_info:
            self.client.make_request(
                "POST",
                "https://api.test.com",
                {"Authorization": "Bearer test"},
                large_data
            )
        
        assert "exceeds maximum allowed size" in str(exc_info.value)
    
    def test_rate_limit_max_retries(self) -> None:
        """Test that rate limiting respects MAX_RETRIES."""
        mock_response = Mock(spec=httpx.Response)
        mock_response.status_code = 429
        mock_response.headers = {"Retry-After": "5"}
        
        self.client._parse_error_response = Mock(return_value=("Rate limit", None))
        
        # At max retries - 1, should raise
        with pytest.raises(RateLimitError) as exc_info:
            self.client._handle_rate_limit(mock_response, attempt=self.client.MAX_RETRIES - 1)
        
        assert f"{self.client.MAX_RETRIES} attempts" in str(exc_info.value)
    
    def test_quota_exceeded_detection(self) -> None:
        """Test that quota exceeded errors are detected properly."""
        mock_response = Mock(spec=httpx.Response)
        mock_response.status_code = 429
        mock_response.headers = {}
        
        # Mock quota exceeded error
        self.client._parse_error_response = Mock(
            return_value=("Your API quota has been exceeded", "quota_exceeded")
        )
        
        with pytest.raises(QuotaExceededError) as exc_info:
            self.client._handle_rate_limit(mock_response, attempt=0)
        
        assert "quota exceeded" in str(exc_info.value).lower()
        assert "https://console.x.ai" in str(exc_info.value)