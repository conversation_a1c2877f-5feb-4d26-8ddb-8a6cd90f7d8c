"""Test shared connection pool implementation."""

import pytest
from unittest.mock import Mock, patch

from llm_grok import <PERSON>rok, cleanup_shared_resources
from llm_grok.grok import _shared_client_pool, _current_api_key, _client_lock
from tests.utils.mocks import TEST_API_KEY


class TestSharedConnectionPool:
    """Test that all Grok instances share the same connection pool."""
    
    def test_multiple_models_share_same_client(self) -> None:
        """Test that multiple model instances share the same GrokClient."""
        # Clean up any existing shared state
        cleanup_shared_resources()
        
        # Create multiple Grok instances
        grok1 = Grok("x-ai/grok-4")
        grok2 = Grok("grok-2-latest")
        grok3 = Grok("grok-3-latest")
        
        # Get clients with same API key
        api_key = TEST_API_KEY
        client1 = grok1._get_client(api_key)
        client2 = grok2._get_client(api_key)
        client3 = grok3._get_client(api_key)
        
        # All should be the same instance
        assert client1 is client2
        assert client2 is client3
        assert client1 is client3
        
        # Clean up
        cleanup_shared_resources()
    
    def test_api_key_change_creates_new_client(self) -> None:
        """Test that changing API key creates a new client and closes the old one."""
        # Clean up any existing shared state
        cleanup_shared_resources()
        
        grok = Grok("x-ai/grok-4")
        
        # Get client with first API key
        client1 = grok._get_client("api-key-1")
        assert client1 is not None
        
        # Mock the close method to verify it's called
        with patch.object(client1, 'close') as mock_close:
            # Get client with different API key
            client2 = grok._get_client("api-key-2")
            
            # Should have called close on the old client
            mock_close.assert_called_once()
            
            # Should be different instances
            assert client1 is not client2
        
        # Clean up
        cleanup_shared_resources()
    
    def test_cleanup_closes_shared_client(self) -> None:
        """Test that cleanup_shared_resources properly closes the client."""
        # Clean up any existing shared state
        cleanup_shared_resources()
        
        grok = Grok("x-ai/grok-4")
        client = grok._get_client(TEST_API_KEY)
        
        # Mock the close method
        with patch.object(client, 'close') as mock_close:
            # Clean up resources
            cleanup_shared_resources()
            
            # Should have called close
            mock_close.assert_called_once()
            
            # Global state should be None
            from llm_grok.grok import _shared_client_pool, _current_api_key
            with _client_lock:
                assert _shared_client_pool is None
                assert _current_api_key is None
    
    def test_thread_safety(self) -> None:
        """Test that client access is thread-safe."""
        import threading
        import time
        
        # Clean up any existing shared state
        cleanup_shared_resources()
        
        results = []
        api_key = TEST_API_KEY
        
        def get_client() -> None:
            grok = Grok("x-ai/grok-4")
            client = grok._get_client(api_key)
            results.append(client)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=get_client)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All results should be the same client instance
        assert len(results) == 10
        first_client = results[0]
        for client in results[1:]:
            assert client is first_client
        
        # Clean up
        cleanup_shared_resources()
    
    def test_no_models_use_instance_client(self) -> None:
        """Verify that Grok instances no longer have _client attribute."""
        grok = Grok("x-ai/grok-4")
        assert not hasattr(grok, '_client')